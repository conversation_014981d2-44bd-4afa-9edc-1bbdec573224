#!/usr/bin/env python3
"""
ImageNet100训练脚本
使用方法:
python train_imagenet100.py --data-path /path/to/your/imagenet100 --batch-size 64 --output ./output
"""

import os
import subprocess
import argparse

def main():
    parser = argparse.ArgumentParser(description='Train vHeat on ImageNet100')
    parser.add_argument('--data-path', type=str, required=True, 
                        help='Path to ImageNet100 dataset')
    parser.add_argument('--batch-size', type=int, default=64,
                        help='Batch size per GPU (default: 64)')
    parser.add_argument('--output', type=str, default='./output',
                        help='Output directory (default: ./output)')
    parser.add_argument('--gpus', type=int, default=1,
                        help='Number of GPUs to use (default: 1)')
    parser.add_argument('--model-size', type=str, default='tiny', 
                        choices=['tiny', 'small', 'base'],
                        help='Model size (default: tiny)')
    parser.add_argument('--epochs', type=int, default=300,
                        help='Number of training epochs (default: 300)')
    parser.add_argument('--resume', type=str, default='',
                        help='Resume from checkpoint')
    parser.add_argument('--pretrained', type=str, default='',
                        help='Use pretrained weights')
    parser.add_argument('--eval-only', action='store_true',
                        help='Only evaluate the model')
    
    args = parser.parse_args()
    
    # 检查数据集路径
    if not os.path.exists(args.data_path):
        raise ValueError(f"Dataset path does not exist: {args.data_path}")
    
    train_path = os.path.join(args.data_path, 'train')
    val_path = os.path.join(args.data_path, 'val')
    
    if not os.path.exists(train_path):
        raise ValueError(f"Train directory does not exist: {train_path}")
    if not os.path.exists(val_path):
        raise ValueError(f"Validation directory does not exist: {val_path}")
    
    # 检查类别数
    train_classes = len([d for d in os.listdir(train_path) 
                        if os.path.isdir(os.path.join(train_path, d))])
    val_classes = len([d for d in os.listdir(val_path) 
                      if os.path.isdir(os.path.join(val_path, d))])
    
    print(f"Found {train_classes} classes in train set")
    print(f"Found {val_classes} classes in validation set")
    
    if train_classes != val_classes:
        print(f"Warning: Train and validation have different number of classes!")
    
    # 选择配置文件
    if args.model_size == 'tiny':
        config_file = 'classification/configs/vHeat/vHeat_tiny_imagenet100.yaml'
    elif args.model_size == 'small':
        config_file = 'classification/configs/vHeat/vHeat_small_imagenet100.yaml'
    else:  # base
        config_file = 'classification/configs/vHeat/vHeat_base_imagenet100.yaml'
    
    # 构建训练命令
    cmd = [
        'python', '-m', 'torch.distributed.launch',
        '--nnodes=1',
        '--node_rank=0',
        f'--nproc_per_node={args.gpus}',
        '--master_addr=127.0.0.1',
        '--master_port=29501',
        'classification/main.py',
        '--cfg', config_file,
        '--batch-size', str(args.batch_size),
        '--data-path', args.data_path,
        '--output', args.output
    ]
    
    # 添加可选参数
    if args.resume:
        cmd.extend(['--resume', args.resume])
    
    if args.pretrained:
        cmd.extend(['--pretrained', args.pretrained])
    
    if args.eval_only:
        cmd.append('--eval')
    
    # 添加自定义配置选项
    cmd.extend([
        '--opts',
        'TRAIN.EPOCHS', str(args.epochs),
        'MODEL.NUM_CLASSES', str(train_classes)
    ])
    
    print("Running command:")
    print(' '.join(cmd))
    print()
    
    # 执行训练
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Training failed with error code {e.returncode}")
        return e.returncode
    
    print("Training completed successfully!")
    return 0

if __name__ == '__main__':
    exit(main())
