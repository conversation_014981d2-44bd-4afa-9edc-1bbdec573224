<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>vHeat分类模型完整流程详解</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .step {
            background: #ecf0f1;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 5px solid #3498db;
        }
        .math {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #dee2e6;
            overflow-x: auto;
        }
        .flow-diagram {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .arrow {
            font-size: 24px;
            color: #3498db;
            text-align: center;
            margin: 10px 0;
        }
        .highlight {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #27ae60;
        }
        .warning {
            background: #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #fdcb6e;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        .matrix {
            display: inline-block;
            border: 1px solid #333;
            padding: 10px;
            margin: 5px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 vHeat分类模型完整流程详解</h1>
        
        <div class="flow-diagram">
            <h2 style="color: white; border: none; padding: 0; margin: 0;">整体流程概览</h2>
            <p>输入图像 → Patch嵌入 → 多层Heat传导 → 全局池化 → 分类预测</p>
        </div>

        <h2>📊 模型架构总览</h2>
        <div class="step">
            <h3>输入输出规格</h3>
            <table>
                <tr>
                    <th>阶段</th>
                    <th>输入尺寸</th>
                    <th>输出尺寸</th>
                    <th>主要操作</th>
                </tr>
                <tr>
                    <td>输入图像</td>
                    <td>[B, 3, 224, 224]</td>
                    <td>[B, 3, 224, 224]</td>
                    <td>RGB图像</td>
                </tr>
                <tr>
                    <td>Patch嵌入</td>
                    <td>[B, 3, 224, 224]</td>
                    <td>[B, 96, 56, 56]</td>
                    <td>4×4卷积分块</td>
                </tr>
                <tr>
                    <td>Stage 1</td>
                    <td>[B, 96, 56, 56]</td>
                    <td>[B, 192, 28, 28]</td>
                    <td>2个HeatBlock + 下采样</td>
                </tr>
                <tr>
                    <td>Stage 2</td>
                    <td>[B, 192, 28, 28]</td>
                    <td>[B, 384, 14, 14]</td>
                    <td>2个HeatBlock + 下采样</td>
                </tr>
                <tr>
                    <td>Stage 3</td>
                    <td>[B, 384, 14, 14]</td>
                    <td>[B, 768, 7, 7]</td>
                    <td>9个HeatBlock + 下采样</td>
                </tr>
                <tr>
                    <td>Stage 4</td>
                    <td>[B, 768, 7, 7]</td>
                    <td>[B, 768, 7, 7]</td>
                    <td>2个HeatBlock</td>
                </tr>
                <tr>
                    <td>分类头</td>
                    <td>[B, 768, 7, 7]</td>
                    <td>[B, 1000]</td>
                    <td>全局池化 + 线性层</td>
                </tr>
            </table>
        </div>

        <h2>🔧 步骤1: Patch嵌入层</h2>
        <div class="step">
            <h3>功能描述</h3>
            <p>将输入的RGB图像分割成patch并进行初始特征提取，类似于ViT的patch embedding，但使用卷积实现。</p>
            
            <h3>数学原理</h3>
            <div class="math">
                输入: I ∈ ℝ^(B×3×224×224)<br>
                卷积核: K ∈ ℝ^(96×3×4×4)<br>
                步长: stride = 4<br>
                输出: F = Conv2D(I, K, stride=4) ∈ ℝ^(B×96×56×56)
            </div>
            
            <h3>具体实现</h3>
            <div class="code">
def patch_embedding(image):
    """
    输入: [B, 3, 224, 224] RGB图像
    输出: [B, 96, 56, 56] patch特征
    """
    # 4×4卷积，stride=4，将224×224分成56×56个patch
    x = conv2d(image, 
               kernel_size=4, 
               stride=4, 
               out_channels=96)
    
    # LayerNorm归一化
    x = layer_norm(x)
    
    return x  # [B, 96, 56, 56]
            </div>
            
            <div class="highlight">
                <strong>关键点:</strong> 每个4×4的图像区域被压缩成一个96维的特征向量，相当于将原图分成56×56=3136个patch。
            </div>
        </div>

        <div class="arrow">⬇️</div>

        <h2>🌡️ 步骤2: Heat传导核心算子</h2>
        <div class="step">
            <h3>Heat2D算子详解</h3>
            <p>这是vHeat的核心创新，通过模拟热传导过程实现全局信息传播。</p>
            
            <h3>物理背景</h3>
            <div class="math">
                热传导方程: ∂u/∂t = k∇²u = k(∂²u/∂x² + ∂²u/∂y²)<br>
                边界条件: ∂u/∂x|_{x=0,L} = 0, ∂u/∂y|_{y=0,L} = 0<br>
                初始条件: u(x,y,0) = φ(x,y)
            </div>
            
            <h3>解析解形式</h3>
            <div class="math">
                u(x,y,t) = Σ Σ A_{n,m} cos(nπx/L) cos(mπy/L) exp(-λ_{n,m}kt)<br>
                其中: λ_{n,m} = (nπ/L)² + (mπ/L)²<br>
                系数: A_{n,m} = DCT2D(φ(x,y))
            </div>
            
            <h3>算法实现步骤</h3>
            <div class="code">
def heat_conduction_operator(x):
    """
    Heat传导算子的完整实现
    输入: x [B, H, W, C]
    输出: heat_processed [B, H, W, C]
    """
    B, H, W, C = x.shape
    
    # 步骤1: 局部特征提取
    x = depthwise_conv3x3(x)  # [B, H, W, C]
    
    # 步骤2: 线性变换和分割
    x = linear_layer(x, C, 2*C)  # [B, H, W, 2C]
    x_main, x_gate = split(x, dim=-1)  # 各自 [B, H, W, C]
    
    # 步骤3: DCT变换到频域
    # 对高度方向做DCT
    x_main = matrix_multiply(dct_weight_h, x_main)
    # 对宽度方向做DCT
    x_main = matrix_multiply(dct_weight_w, x_main)
    
    # 步骤4: 应用热传导衰减
    for h in range(H):
        for w in range(W):
            lambda_hw = (h*π/H)² + (w*π/W)²
            decay = exp(-lambda_hw * k)
            x_main[:, h, w, :] *= decay
    
    # 步骤5: IDCT逆变换回空间域
    x_main = matrix_multiply(dct_weight_h.T, x_main)
    x_main = matrix_multiply(dct_weight_w.T, x_main)
    
    # 步骤6: 门控机制
    x_main = x_main * silu(x_gate)
    
    return x_main
            </div>
        </div>

        <div class="arrow">⬇️</div>

        <h2>🏗️ 步骤3: HeatBlock结构</h2>
        <div class="step">
            <h3>单个HeatBlock的组成</h3>
            <p>每个HeatBlock包含Heat传导算子、MLP前馈网络和残差连接。</p>
            
            <div class="code">
def heat_block(x, dim, resolution):
    """
    单个Heat块的完整实现
    输入: x [B, dim, resolution, resolution]
    输出: processed_x [B, dim, resolution, resolution]
    """
    # 预归一化
    x_norm = layer_norm(x)
    
    # Heat传导算子
    x_heat = heat_conduction_operator(x_norm)
    
    # 第一个残差连接
    x = x + dropout(x_heat)
    
    # MLP前馈网络
    x_norm2 = layer_norm(x)
    x_mlp = mlp_block(x_norm2, dim, 4*dim)  # 4倍扩展
    
    # 第二个残差连接
    x = x + dropout(x_mlp)
    
    return x
            </div>
            
            <div class="highlight">
                <strong>设计理念:</strong> 类似Transformer的结构，但用Heat传导算子替代了自注意力机制。
            </div>
        </div>

        <div class="arrow">⬇️</div>

        <h2>📈 步骤4: 多阶段特征提取</h2>
        <div class="step">
            <h3>层次化架构</h3>
            <p>采用类似Swin Transformer的层次化设计，逐步降低分辨率，增加通道数。</p>
            
            <table>
                <tr>
                    <th>阶段</th>
                    <th>分辨率</th>
                    <th>通道数</th>
                    <th>HeatBlock数量</th>
                    <th>作用</th>
                </tr>
                <tr>
                    <td>Stage 1</td>
                    <td>56×56</td>
                    <td>96</td>
                    <td>2</td>
                    <td>浅层特征提取</td>
                </tr>
                <tr>
                    <td>Stage 2</td>
                    <td>28×28</td>
                    <td>192</td>
                    <td>2</td>
                    <td>中层特征融合</td>
                </tr>
                <tr>
                    <td>Stage 3</td>
                    <td>14×14</td>
                    <td>384</td>
                    <td>9</td>
                    <td>深层特征提取（主要）</td>
                </tr>
                <tr>
                    <td>Stage 4</td>
                    <td>7×7</td>
                    <td>768</td>
                    <td>2</td>
                    <td>高级语义特征</td>
                </tr>
            </table>
            
            <h3>下采样操作</h3>
            <div class="code">
def downsample_layer(x, out_dim):
    """
    下采样层：降低分辨率，增加通道数
    """
    # 2×2卷积，stride=2
    x = conv2d(x, kernel_size=2, stride=2, out_channels=out_dim)
    x = layer_norm(x)
    return x
            </div>
        </div>

        <div class="arrow">⬇️</div>

        <h2>🎯 步骤5: 分类头</h2>
        <div class="step">
            <h3>全局特征聚合</h3>
            <p>将最终的特征图转换为分类预测。</p>
            
            <div class="code">
def classification_head(x):
    """
    分类头实现
    输入: x [B, 768, 7, 7] - 最后一层的特征图
    输出: logits [B, num_classes] - 分类概率
    """
    # 全局平均池化
    global_features = global_average_pooling(x)  # [B, 768]
    
    # LayerNorm归一化
    global_features = layer_norm(global_features)
    
    # 线性分类器
    logits = linear_classifier(global_features, 768, 1000)  # [B, 1000]
    
    return logits
            </div>
            
            <div class="math">
                全局平均池化: f_global = (1/HW) Σ Σ f(i,j)<br>
                线性分类: logits = W·f_global + b<br>
                其中: W ∈ ℝ^(1000×768), b ∈ ℝ^1000
            </div>
        </div>

        <h2>🔬 核心数学原理深度解析</h2>
        <div class="step">
            <h3>DCT变换的数学基础</h3>
            <div class="math">
                DCT公式: F(n,m) = C(n)C(m) Σ Σ f(i,j) cos((2i+1)nπ/2N) cos((2j+1)mπ/2M)<br>
                归一化系数: C(k) = √(1/N) if k=0, √(2/N) if k>0
            </div>
            
            <h3>热传导衰减的物理意义</h3>
            <div class="warning">
                <strong>关键洞察:</strong> 不同频率分量的衰减速度不同：
                <ul>
                    <li>低频分量（大尺度变化）：衰减慢，信息传播远</li>
                    <li>高频分量（局部细节）：衰减快，影响范围小</li>
                </ul>
            </div>
            
            <h3>复杂度分析</h3>
            <table>
                <tr>
                    <th>方法</th>
                    <th>时间复杂度</th>
                    <th>空间复杂度</th>
                    <th>全局感受野</th>
                </tr>
                <tr>
                    <td>传统自注意力</td>
                    <td>O(N²)</td>
                    <td>O(N²)</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>vHeat (DCT/IDCT)</td>
                    <td>O(N^1.5)</td>
                    <td>O(N)</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>传统CNN</td>
                    <td>O(N)</td>
                    <td>O(N)</td>
                    <td>❌</td>
                </tr>
            </table>
        </div>

        <h2>📊 实验结果与性能</h2>
        <div class="step">
            <h3>ImageNet-1K分类结果</h3>
            <table>
                <tr>
                    <th>模型</th>
                    <th>参数量(M)</th>
                    <th>FLOPs(G)</th>
                    <th>Top-1准确率(%)</th>
                    <th>吞吐量(images/s)</th>
                </tr>
                <tr>
                    <td>Swin-T</td>
                    <td>29</td>
                    <td>4.5</td>
                    <td>81.2</td>
                    <td>1244</td>
                </tr>
                <tr>
                    <td><strong>vHeat-T</strong></td>
                    <td>28</td>
                    <td>4.0</td>
                    <td><strong>82.2</strong></td>
                    <td><strong>1514</strong></td>
                </tr>
            </table>
            
            <div class="highlight">
                <strong>性能优势:</strong>
                <ul>
                    <li>准确率提升: 82.2% vs 81.2% (+1.0%)</li>
                    <li>速度提升: 1514 vs 1244 images/s (+21.7%)</li>
                    <li>计算效率: 4.0G vs 4.5G FLOPs (-11.1%)</li>
                </ul>
            </div>
        </div>

        <h2>🎯 总结与创新点</h2>
        <div class="step">
            <h3>核心创新</h3>
            <ol>
                <li><strong>物理启发设计:</strong> 将热传导物理过程引入深度学习</li>
                <li><strong>数学优雅性:</strong> 利用DCT/IDCT高效实现全局感受野</li>
                <li><strong>计算高效性:</strong> O(N^1.5)复杂度优于传统注意力机制</li>
                <li><strong>可解释性:</strong> 每个参数都有明确的物理意义</li>
            </ol>
            
            <h3>适用场景</h3>
            <ul>
                <li>图像分类任务</li>
                <li>目标检测</li>
                <li>语义分割</li>
                <li>需要全局感受野的视觉任务</li>
            </ul>
        </div>

        <h2>🔍 详细数学推导过程</h2>
        <div class="step">
            <h3>热传导方程的完整求解</h3>

            <h4>1. 分离变量法</h4>
            <div class="math">
                设 u(x,y,t) = X(x)Y(y)T(t)<br>
                代入热传导方程: ∂u/∂t = k∇²u<br>
                得到: X(x)Y(y)T'(t) = k[X''(x)Y(y)T(t) + X(x)Y''(y)T(t)]<br>
                分离变量: T'(t)/T(t) = k[X''(x)/X(x) + Y''(y)/Y(y)] = -kλ
            </div>

            <h4>2. 边界条件求解</h4>
            <div class="math">
                边界条件: ∂u/∂x|_{x=0,L} = 0, ∂u/∂y|_{y=0,L} = 0<br>
                对于X(x): X''(x) + α²X(x) = 0, X'(0) = X'(L) = 0<br>
                解得: X_n(x) = cos(nπx/L), α_n = nπ/L<br>
                类似地: Y_m(y) = cos(mπy/L), β_m = mπ/L<br>
                特征值: λ_{n,m} = α_n² + β_m² = (nπ/L)² + (mπ/L)²
            </div>

            <h4>3. 通解构造</h4>
            <div class="math">
                u(x,y,t) = Σ Σ A_{n,m} cos(nπx/L) cos(mπy/L) exp(-λ_{n,m}kt)<br>
                系数确定: A_{n,m} = (4/L²) ∫∫ φ(x,y) cos(nπx/L) cos(mπy/L) dxdy<br>
                <strong>关键发现: A_{n,m} ≈ DCT2D(φ(x,y))</strong>
            </div>
        </div>

        <h2>💻 完整代码实现</h2>
        <div class="step">
            <h3>vHeat完整前向传播</h3>
            <div class="code">
class vHeatClassifier:
    def __init__(self, num_classes=1000):
        # Patch嵌入层
        self.patch_embed = PatchEmbedding(
            patch_size=4, in_channels=3, embed_dim=96
        )

        # 多阶段Heat块
        self.stages = [
            Stage(dim=96, depth=2, resolution=56),   # Stage 1
            Stage(dim=192, depth=2, resolution=28),  # Stage 2
            Stage(dim=384, depth=9, resolution=14),  # Stage 3
            Stage(dim=768, depth=2, resolution=7),   # Stage 4
        ]

        # 分类头
        self.classifier = ClassificationHead(768, num_classes)

    def forward(self, x):
        # 输入: [B, 3, 224, 224]

        # Patch嵌入
        x = self.patch_embed(x)  # [B, 96, 56, 56]

        # 多阶段处理
        for i, stage in enumerate(self.stages):
            x = stage(x)
            if i < len(self.stages) - 1:
                x = self.downsample(x)  # 下采样

        # 分类预测
        logits = self.classifier(x)  # [B, 1000]

        return logits

class Heat2D:
    def __init__(self, dim, resolution):
        self.dim = dim
        self.resolution = resolution

        # 生成DCT变换矩阵
        self.dct_weight = self.get_dct_matrix(resolution)

        # 生成热传导衰减图
        self.decay_map = self.get_decay_map(resolution)

        # 可学习的热传导系数
        self.k_param = nn.Parameter(torch.ones(dim))

    def get_dct_matrix(self, N):
        """生成DCT变换矩阵"""
        weight = torch.zeros(N, N)
        for n in range(N):
            for x in range(N):
                weight[n, x] = math.cos((x + 0.5) * n * math.pi / N)
                weight[n, x] *= math.sqrt(2 / N)
                if n == 0:
                    weight[n, x] /= math.sqrt(2)
        return weight

    def get_decay_map(self, resolution):
        """生成热传导衰减图"""
        decay = torch.zeros(resolution, resolution)
        for n in range(resolution):
            for m in range(resolution):
                lambda_nm = (n * math.pi / resolution)**2 + \
                           (m * math.pi / resolution)**2
                decay[n, m] = math.exp(-lambda_nm)
        return decay

    def forward(self, x):
        B, C, H, W = x.shape

        # 深度卷积
        x = self.dwconv(x)

        # 线性变换和分割
        x = self.linear(x.permute(0, 2, 3, 1))  # [B, H, W, 2C]
        x, gate = x.chunk(2, dim=-1)  # [B, H, W, C]

        # DCT变换
        x = self.apply_dct(x)

        # 热传导衰减
        decay_factors = torch.pow(self.decay_map, self.k_param)
        x = x * decay_factors

        # IDCT逆变换
        x = self.apply_idct(x)

        # 门控机制
        x = x * F.silu(gate)

        return x.permute(0, 3, 1, 2)  # [B, C, H, W]
            </div>
        </div>

        <h2>📈 性能分析与对比</h2>
        <div class="step">
            <h3>计算复杂度详细分析</h3>

            <h4>传统自注意力机制</h4>
            <div class="math">
                Q, K, V = Linear(X)  # O(N·d²)<br>
                Attention = Softmax(QK^T/√d)  # O(N²·d)<br>
                Output = Attention·V  # O(N²·d)<br>
                总复杂度: O(N²·d + N·d²)
            </div>

            <h4>vHeat Heat2D算子</h4>
            <div class="math">
                DCT变换: O(N·log N·d)  # 快速DCT算法<br>
                衰减应用: O(N·d)  # 逐元素乘法<br>
                IDCT变换: O(N·log N·d)  # 快速IDCT算法<br>
                总复杂度: O(N·log N·d) ≈ O(N^1.5·d)
            </div>

            <h3>内存使用对比</h3>
            <table>
                <tr>
                    <th>操作</th>
                    <th>自注意力</th>
                    <th>vHeat</th>
                    <th>优势</th>
                </tr>
                <tr>
                    <td>注意力矩阵</td>
                    <td>O(N²)</td>
                    <td>-</td>
                    <td>无需存储</td>
                </tr>
                <tr>
                    <td>DCT系数</td>
                    <td>-</td>
                    <td>O(N)</td>
                    <td>线性空间</td>
                </tr>
                <tr>
                    <td>中间结果</td>
                    <td>O(N²)</td>
                    <td>O(N)</td>
                    <td>显著减少</td>
                </tr>
            </table>
        </div>

        <h2>🔬 消融实验分析</h2>
        <div class="step">
            <h3>关键组件的作用</h3>
            <table>
                <tr>
                    <th>配置</th>
                    <th>Top-1准确率(%)</th>
                    <th>FLOPs(G)</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>完整vHeat</td>
                    <td><strong>82.2</strong></td>
                    <td>4.0</td>
                    <td>完整模型</td>
                </tr>
                <tr>
                    <td>无Heat算子</td>
                    <td>79.8</td>
                    <td>3.8</td>
                    <td>用普通卷积替代</td>
                </tr>
                <tr>
                    <td>无门控机制</td>
                    <td>81.5</td>
                    <td>4.0</td>
                    <td>移除SiLU门控</td>
                </tr>
                <tr>
                    <td>固定k参数</td>
                    <td>81.0</td>
                    <td>4.0</td>
                    <td>k不可学习</td>
                </tr>
            </table>

            <div class="highlight">
                <strong>关键发现:</strong>
                <ul>
                    <li>Heat算子贡献最大(+2.4%准确率提升)</li>
                    <li>门控机制提供额外的非线性(+0.7%)</li>
                    <li>可学习k参数优化传导强度(+1.2%)</li>
                </ul>
            </div>
        </div>

        <h2>🎯 应用场景与扩展</h2>
        <div class="step">
            <h3>多任务性能表现</h3>
            <table>
                <tr>
                    <th>任务</th>
                    <th>数据集</th>
                    <th>vHeat性能</th>
                    <th>基线对比</th>
                    <th>提升</th>
                </tr>
                <tr>
                    <td>图像分类</td>
                    <td>ImageNet-1K</td>
                    <td>82.2% Top-1</td>
                    <td>Swin-T: 81.2%</td>
                    <td>+1.0%</td>
                </tr>
                <tr>
                    <td>目标检测</td>
                    <td>COCO</td>
                    <td>45.1 box mAP</td>
                    <td>Swin-T: 42.7</td>
                    <td>+2.4</td>
                </tr>
                <tr>
                    <td>语义分割</td>
                    <td>ADE20K</td>
                    <td>47.0 mIoU</td>
                    <td>Swin-T: 44.4</td>
                    <td>+2.6</td>
                </tr>
            </table>

            <h3>模型变体</h3>
            <ul>
                <li><strong>vHeat-T:</strong> 28M参数，适合资源受限场景</li>
                <li><strong>vHeat-S:</strong> 50M参数，平衡性能与效率</li>
                <li><strong>vHeat-B:</strong> 88M参数，追求最佳性能</li>
                <li><strong>vHeat-L:</strong> 197M参数，大规模应用</li>
            </ul>
        </div>

        <div class="flow-diagram">
            <h3 style="color: white; border: none; margin: 0;">vHeat的核心价值</h3>
            <p>将经典物理学的热传导原理巧妙地转化为高效的深度学习算子，<br>
            实现了理论优雅性与实际性能的完美结合！</p>
            <br>
            <p><strong>🔥 创新意义:</strong> 为深度学习模型设计提供了全新的物理启发范式</p>
        </div>
    </div>
</body>
</html>
