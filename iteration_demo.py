"""
有限差分法迭代过程演示
清晰展示每一步的计算过程
"""

import numpy as np
import matplotlib.pyplot as plt

def demonstrate_iteration_formula():
    """演示迭代公式的具体计算过程"""
    print("🔄 有限差分法迭代公式演示")
    print("=" * 50)
    
    # 创建一个小网格便于观察
    nx, ny = 5, 5
    dx, dy = 0.25, 0.25
    k = 0.1
    dt = 0.01  # 小时间步长保证稳定
    
    print(f"网格大小: {nx} × {ny}")
    print(f"网格间距: dx = {dx}, dy = {dy}")
    print(f"时间步长: dt = {dt}")
    print(f"热传导系数: k = {k}")
    
    # 初始温度分布：中心热，四周冷
    u = np.zeros((ny, nx))
    u[2, 2] = 100.0  # 中心点100度
    
    print(f"\n📊 初始温度分布:")
    print_temperature_grid(u)
    
    # 演示几个迭代步骤
    for step in range(3):
        print(f"\n🔄 第 {step+1} 次迭代:")
        print("-" * 30)
        
        u_new = u.copy()
        
        # 对每个内部点应用迭代公式
        for i in range(1, ny-1):
            for j in range(1, nx-1):
                # 计算拉普拉斯算子
                d2u_dx2 = (u[i, j+1] - 2*u[i, j] + u[i, j-1]) / dx**2
                d2u_dy2 = (u[i+1, j] - 2*u[i, j] + u[i-1, j]) / dy**2
                laplacian = d2u_dx2 + d2u_dy2
                
                # 应用迭代公式
                u_new[i, j] = u[i, j] + k * dt * laplacian
                
                # 详细展示中心点的计算
                if i == 2 and j == 2:
                    print(f"中心点 ({i},{j}) 的计算过程:")
                    print(f"  当前温度: u[{i},{j}] = {u[i,j]:.2f}")
                    print(f"  邻居温度: 上={u[i-1,j]:.2f}, 下={u[i+1,j]:.2f}, 左={u[i,j-1]:.2f}, 右={u[i,j+1]:.2f}")
                    print(f"  x方向二阶导数: ({u[i,j+1]:.2f} - 2×{u[i,j]:.2f} + {u[i,j-1]:.2f}) / {dx**2:.3f} = {d2u_dx2:.2f}")
                    print(f"  y方向二阶导数: ({u[i+1,j]:.2f} - 2×{u[i,j]:.2f} + {u[i-1,j]:.2f}) / {dy**2:.3f} = {d2u_dy2:.2f}")
                    print(f"  拉普拉斯算子: {d2u_dx2:.2f} + {d2u_dy2:.2f} = {laplacian:.2f}")
                    print(f"  温度变化: {k} × {dt} × {laplacian:.2f} = {k*dt*laplacian:.2f}")
                    print(f"  新温度: {u[i,j]:.2f} + {k*dt*laplacian:.2f} = {u_new[i,j]:.2f}")
        
        # 边界条件：四周保持0度
        u_new[0, :] = 0
        u_new[-1, :] = 0
        u_new[:, 0] = 0
        u_new[:, -1] = 0
        
        u = u_new
        print(f"\n更新后的温度分布:")
        print_temperature_grid(u)

def print_temperature_grid(u):
    """打印温度网格"""
    ny, nx = u.shape
    print("    ", end="")
    for j in range(nx):
        print(f"  j={j}  ", end="")
    print()
    
    for i in range(ny):
        print(f"i={i} ", end="")
        for j in range(nx):
            print(f"{u[i,j]:6.2f}", end="")
        print()

def compare_iteration_vs_analytical():
    """对比迭代解与解析解"""
    print("\n🆚 迭代解 vs 解析解对比")
    print("=" * 40)
    
    # 参数设置
    nx, ny = 50, 50
    lx, ly = 1.0, 1.0
    k = 0.1
    
    dx = lx / (nx - 1)
    dy = ly / (ny - 1)
    dt = 0.25 * min(dx**2, dy**2) / (2 * k)  # 稳定性条件
    
    # 创建网格
    x = np.linspace(0, lx, nx)
    y = np.linspace(0, ly, ny)
    X, Y = np.meshgrid(x, y)
    
    # 初始条件：高斯分布
    u_initial = 100 * np.exp(-((X-0.5)**2 + (Y-0.5)**2) / 0.05)
    
    # 方法1：有限差分迭代
    print("🔄 有限差分法求解中...")
    u_fdm = u_initial.copy()
    target_time = 0.05
    n_steps = int(target_time / dt)
    
    print(f"目标时间: {target_time}")
    print(f"需要迭代步数: {n_steps}")
    print(f"每步时间: {dt:.6f}")
    
    for step in range(n_steps):
        u_new = u_fdm.copy()
        
        # 迭代公式
        for i in range(1, ny-1):
            for j in range(1, nx-1):
                d2u_dx2 = (u_fdm[i, j+1] - 2*u_fdm[i, j] + u_fdm[i, j-1]) / dx**2
                d2u_dy2 = (u_fdm[i+1, j] - 2*u_fdm[i, j] + u_fdm[i-1, j]) / dy**2
                u_new[i, j] = u_fdm[i, j] + k * dt * (d2u_dx2 + d2u_dy2)
        
        # 边界条件
        u_new[0, :] = u_new[-1, :] = u_new[:, 0] = u_new[:, -1] = 0
        u_fdm = u_new
        
        if step % (n_steps // 5) == 0:
            progress = step / n_steps * 100
            print(f"  进度: {progress:.1f}%, 最高温度: {np.max(u_fdm):.2f}")
    
    # 方法2：简化的解析解（使用FFT近似）
    print("\n⚡ 解析解计算中...")
    
    # 创建频域特征值
    kx = np.fft.fftfreq(nx, dx) * 2 * np.pi
    ky = np.fft.fftfreq(ny, dy) * 2 * np.pi
    KX, KY = np.meshgrid(kx, ky)
    eigenvalues = KX**2 + KY**2
    
    # FFT到频域
    u_freq = np.fft.fft2(u_initial)
    
    # 应用时间演化
    u_freq_evolved = u_freq * np.exp(-eigenvalues * k * target_time)
    
    # 逆FFT回空间域
    u_analytical = np.real(np.fft.ifft2(u_freq_evolved))
    
    # 计算误差
    error = np.abs(u_fdm - u_analytical)
    max_error = np.max(error)
    mean_error = np.mean(error)
    
    print(f"\n📊 结果对比:")
    print(f"有限差分法最高温度: {np.max(u_fdm):.4f}")
    print(f"解析解最高温度: {np.max(u_analytical):.4f}")
    print(f"最大绝对误差: {max_error:.6f}")
    print(f"平均绝对误差: {mean_error:.6f}")
    print(f"相对误差: {mean_error/np.max(u_analytical)*100:.4f}%")
    
    # 可视化对比
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 有限差分解
    im1 = axes[0].imshow(u_fdm, cmap='hot', origin='lower')
    axes[0].set_title(f'有限差分法\n({n_steps}次迭代)')
    axes[0].set_xlabel('x')
    axes[0].set_ylabel('y')
    plt.colorbar(im1, ax=axes[0])
    
    # 解析解
    im2 = axes[1].imshow(u_analytical, cmap='hot', origin='lower')
    axes[1].set_title('解析解\n(一步计算)')
    axes[1].set_xlabel('x')
    axes[1].set_ylabel('y')
    plt.colorbar(im2, ax=axes[1])
    
    # 误差分布
    im3 = axes[2].imshow(error, cmap='viridis', origin='lower')
    axes[2].set_title(f'绝对误差\n最大误差: {max_error:.4f}')
    axes[2].set_xlabel('x')
    axes[2].set_ylabel('y')
    plt.colorbar(im3, ax=axes[2])
    
    plt.tight_layout()
    plt.savefig('iteration_vs_analytical.png', dpi=150, bbox_inches='tight')
    plt.show()

def explain_iteration_concept():
    """解释迭代的概念"""
    print("\n💡 迭代概念详解")
    print("=" * 30)
    
    print("🔄 什么是迭代？")
    print("迭代就是重复应用同一个公式，逐步逼近真实解")
    print()
    
    print("📐 迭代公式的组成部分：")
    print("u_new[i,j] = u[i,j] + k * dt * laplacian")
    print("    ↑           ↑         ↑        ↑")
    print("  新值      当前值    步长    变化率")
    print()
    
    print("🎯 类比理解：")
    print("就像GPS导航一样：")
    print("• 当前位置 = u[i,j]")
    print("• 移动方向 = laplacian（热量流动方向）")
    print("• 移动步长 = k * dt")
    print("• 新位置 = u_new[i,j]")
    print()
    
    print("⏰ 为什么需要很多步？")
    print("• 每一步只能移动一小段距离（dt很小）")
    print("• 需要很多小步才能到达目标时间")
    print("• 这保证了数值稳定性")
    print()
    
    print("🚀 vHeat的优势：")
    print("• 不需要迭代！")
    print("• 直接计算任意时间的解")
    print("• 就像有了传送门，一步到位！")

def main():
    """主函数"""
    print("🎓 有限差分法迭代公式详解")
    print("=" * 50)
    
    # 1. 演示迭代公式
    demonstrate_iteration_formula()
    
    # 2. 解释迭代概念
    explain_iteration_concept()
    
    # 3. 对比迭代解与解析解
    compare_iteration_vs_analytical()
    
    print("\n✅ 演示完成！")
    print("\n🎯 关键要点：")
    print("1. 有限差分法确实是用差分近似微分")
    print("2. 您提到的公式就是核心迭代公式")
    print("3. 需要很多次迭代才能到达目标时间")
    print("4. vHeat避开了迭代，直接计算解析解")

if __name__ == "__main__":
    main()
