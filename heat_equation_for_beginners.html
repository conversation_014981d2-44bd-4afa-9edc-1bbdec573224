<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热传导方程求解 - 零基础详解</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.8;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            border-bottom: 4px solid #e74c3c;
            padding-bottom: 15px;
        }
        h2 {
            color: #e74c3c;
            font-size: 1.8em;
            margin-top: 40px;
            border-left: 6px solid #e74c3c;
            padding-left: 20px;
            background: #ffeaa7;
            padding: 15px 20px;
            border-radius: 8px;
        }
        h3 {
            color: #2980b9;
            font-size: 1.4em;
            margin-top: 30px;
        }
        .step {
            background: #ecf0f1;
            padding: 25px;
            margin: 25px 0;
            border-radius: 10px;
            border-left: 6px solid #3498db;
            position: relative;
        }
        .step-number {
            position: absolute;
            top: -15px;
            left: 20px;
            background: #3498db;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
        }
        .intuition {
            background: #d5f4e6;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #27ae60;
            margin: 15px 0;
        }
        .intuition::before {
            content: "💡 直觉理解：";
            font-weight: bold;
            color: #27ae60;
            display: block;
            margin-bottom: 10px;
        }
        .math-simple {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Times New Roman', serif;
            font-size: 1.1em;
            margin: 15px 0;
            border-left: 5px solid #ffc107;
            text-align: center;
        }
        .example {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #28a745;
            margin: 15px 0;
        }
        .example::before {
            content: "🔍 具体例子：";
            font-weight: bold;
            color: #28a745;
            display: block;
            margin-bottom: 10px;
        }
        .warning {
            background: #f8d7da;
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid #dc3545;
            margin: 15px 0;
        }
        .warning::before {
            content: "⚠️ 注意：";
            font-weight: bold;
            color: #dc3545;
            display: block;
            margin-bottom: 10px;
        }
        .visual-box {
            background: #f8f9fa;
            border: 2px dashed #6c757d;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            border-radius: 10px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .key-insight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin: 25px 0;
            text-align: center;
            font-size: 1.2em;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .comparison-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .formula-breakdown {
            background: #f0f8ff;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #87ceeb;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌡️ 热传导方程求解过程详解</h1>
        <p style="text-align: center; font-size: 1.2em; color: #7f8c8d;">
            <strong>专为零基础同学设计 - 从物理直觉到数学求解</strong>
        </p>

        <div class="progress-bar">
            <div class="progress-fill" style="width: 0%" id="progress"></div>
        </div>

        <h2>🎯 我们要解决什么问题？</h2>
        <div class="step">
            <div class="step-number">1</div>
            <h3>生活中的热传导现象</h3>
            
            <div class="intuition">
                想象你手里拿着一杯热咖啡：
                <ul>
                    <li>☕ 咖啡杯最热（热源）</li>
                    <li>🤲 热量传到你的手</li>
                    <li>🌬️ 再传到周围空气</li>
                    <li>⏰ 随着时间推移，整体温度趋于平衡</li>
                </ul>
            </div>

            <div class="visual-box">
                <h4>热传导的三个关键要素</h4>
                <p><strong>空间：</strong>热量在不同位置之间传播</p>
                <p><strong>时间：</strong>温度随时间变化</p>
                <p><strong>材料：</strong>不同材料传热能力不同</p>
            </div>

            <div class="example">
                <strong>日常例子：</strong>
                <ul>
                    <li>🍳 平底锅加热：热量从火源传到锅底，再传到食物</li>
                    <li>🏠 暖气片：热水在管道中，热量传到房间空气</li>
                    <li>🧊 冰块融化：周围热量传到冰块，使其温度升高</li>
                </ul>
            </div>
        </div>

        <h2>📐 建立数学模型</h2>
        <div class="step">
            <div class="step-number">2</div>
            <h3>从物理现象到数学语言</h3>
            
            <div class="intuition">
                我们需要用数学来描述"温度如何随时间和位置变化"
            </div>

            <h4>基本符号定义</h4>
            <div class="formula-breakdown">
                <p><strong>u(x,y,t)</strong> = 位置(x,y)在时间t的温度</p>
                <ul>
                    <li><strong>x, y</strong>：空间坐标（比如：桌面上的位置）</li>
                    <li><strong>t</strong>：时间（比如：第几秒）</li>
                    <li><strong>u</strong>：温度值（比如：30°C）</li>
                </ul>
            </div>

            <div class="example">
                <strong>具体理解：</strong><br>
                u(2, 3, 5) = 45 表示：<br>
                在位置(2,3)，第5秒时，温度是45°C
            </div>

            <h4>热传导的物理规律</h4>
            <div class="math-simple">
                <strong>核心思想：</strong>热量总是从高温流向低温<br>
                <strong>流动速度：</strong>温度差越大，流动越快
            </div>
        </div>

        <h2>🔬 热传导方程的诞生</h2>
        <div class="step">
            <div class="step-number">3</div>
            <h3>从物理定律到数学方程</h3>
            
            <div class="intuition">
                物理学家发现：某个位置温度的变化速度，取决于周围温度的分布情况
            </div>

            <h4>温度变化率的概念</h4>
            <div class="formula-breakdown">
                <p><strong>∂u/∂t</strong>：温度随时间的变化率</p>
                <ul>
                    <li>如果 ∂u/∂t > 0：温度在升高 🔥</li>
                    <li>如果 ∂u/∂t < 0：温度在降低 ❄️</li>
                    <li>如果 ∂u/∂t = 0：温度不变 ⚖️</li>
                </ul>
            </div>

            <h4>空间温度梯度</h4>
            <div class="formula-breakdown">
                <p><strong>∂²u/∂x²</strong>：x方向的温度弯曲程度</p>
                <p><strong>∂²u/∂y²</strong>：y方向的温度弯曲程度</p>
                <ul>
                    <li>正值：该点比周围冷，会被加热</li>
                    <li>负值：该点比周围热，会被冷却</li>
                    <li>零值：该点与周围温度平衡</li>
                </ul>
            </div>

            <div class="key-insight">
                <h4>🎯 热传导方程</h4>
                <div class="math-simple" style="background: rgba(255,255,255,0.2); border: none;">
                    ∂u/∂t = k(∂²u/∂x² + ∂²u/∂y²)
                </div>
                <p><strong>用人话说：</strong>温度变化速度 = 热传导系数 × 周围温度的不平衡程度</p>
            </div>

            <div class="example">
                <strong>参数k的物理意义：</strong>
                <ul>
                    <li>k大：材料导热好（如铜），温度变化快</li>
                    <li>k小：材料导热差（如木头），温度变化慢</li>
                </ul>
            </div>
        </div>

        <h2>🎯 设定边界条件</h2>
        <div class="step">
            <div class="step-number">4</div>
            <h3>告诉方程我们研究什么情况</h3>
            
            <div class="intuition">
                就像做数学题需要已知条件一样，求解方程也需要边界条件
            </div>

            <h4>绝热边界条件</h4>
            <div class="math-simple">
                ∂u/∂x|<sub>x=0,L</sub> = 0<br>
                ∂u/∂y|<sub>y=0,L</sub> = 0
            </div>

            <div class="formula-breakdown">
                <p><strong>物理含义：</strong>在边界处，热量不能流出</p>
                <ul>
                    <li>想象一个完全隔热的盒子</li>
                    <li>盒子内部可以传热</li>
                    <li>但热量不会跑到盒子外面</li>
                </ul>
            </div>

            <div class="example">
                <strong>生活类比：</strong><br>
                就像保温饭盒，内部食物之间可以传热，但热量不会散失到外界
            </div>

            <h4>初始条件</h4>
            <div class="math-simple">
                u(x,y,0) = φ(x,y)
            </div>
            <div class="formula-breakdown">
                <p><strong>含义：</strong>告诉我们t=0时刻，每个位置的初始温度</p>
                <p>φ(x,y)就是"初始温度分布图"</p>
            </div>
        </div>

        <h2>🔧 分离变量法 - 化繁为简</h2>
        <div class="step">
            <div class="step-number">5</div>
            <h3>把复杂问题拆解成简单问题</h3>

            <div class="intuition">
                就像拆解乐高积木一样，我们把复杂的三维问题（x,y,t）拆成三个一维问题
            </div>

            <h4>核心思想</h4>
            <div class="formula-breakdown">
                <p><strong>假设解的形式：</strong></p>
                <div class="math-simple">
                    u(x,y,t) = X(x) × Y(y) × T(t)
                </div>
                <p><strong>含义：</strong>总的温度分布 = x方向的模式 × y方向的模式 × 时间的变化</p>
            </div>

            <div class="example">
                <strong>生活类比：</strong><br>
                就像描述一个人：总印象 = 身高 × 体重 × 年龄的综合效果<br>
                每个因素独立变化，但共同决定整体特征
            </div>

            <h4>代入原方程</h4>
            <div class="math-simple">
                原方程：∂u/∂t = k(∂²u/∂x² + ∂²u/∂y²)<br><br>
                代入 u = X(x)Y(y)T(t)：<br>
                X(x)Y(y)T'(t) = k[X''(x)Y(y)T(t) + X(x)Y''(y)T(t)]
            </div>

            <div class="warning">
                这里用到了求导的乘积法则，不用担心具体计算，重点理解思路
            </div>
        </div>

        <h2>⚖️ 神奇的常数分离</h2>
        <div class="step">
            <div class="step-number">6</div>
            <h3>发现隐藏的数学规律</h3>

            <div class="intuition">
                通过巧妙的数学变换，我们发现每个方向都有自己独立的"特征"
            </div>

            <h4>除法的魔法</h4>
            <div class="formula-breakdown">
                <p>将上面的方程两边同时除以 X(x)Y(y)T(t)：</p>
                <div class="math-simple">
                    T'(t)/T(t) = k[X''(x)/X(x) + Y''(y)/Y(y)]
                </div>
            </div>

            <div class="key-insight">
                <h4>🎯 关键发现</h4>
                <p>左边只依赖时间t，右边只依赖空间x,y</p>
                <p>既然它们相等，那么两边都必须等于同一个<span class="highlight">常数</span>！</p>
                <div class="math-simple" style="background: rgba(255,255,255,0.2); border: none;">
                    T'(t)/T(t) = k[X''(x)/X(x) + Y''(y)/Y(y)] = -kλ
                </div>
            </div>

            <div class="example">
                <strong>为什么是常数？</strong><br>
                想象：如果左边随时间变化，右边随位置变化，它们怎么可能始终相等？<br>
                唯一的可能就是：它们都等于一个固定的数！
            </div>

            <h4>进一步分离</h4>
            <div class="math-simple">
                从 X''(x)/X(x) + Y''(y)/Y(y) = -λ<br><br>
                可以继续分离：<br>
                X''(x)/X(x) = -α²<br>
                Y''(y)/Y(y) = -β²<br>
                其中：α² + β² = λ
            </div>
        </div>

        <h2>🎵 寻找"音符" - 特征函数</h2>
        <div class="step">
            <div class="step-number">7</div>
            <h3>解决每个简单的一维问题</h3>

            <div class="intuition">
                现在我们有三个简单的方程，就像找音乐的基本音符一样
            </div>

            <h4>X方向的方程</h4>
            <div class="math-simple">
                X''(x) + α²X(x) = 0<br>
                边界条件：X'(0) = 0, X'(L) = 0
            </div>

            <div class="formula-breakdown">
                <p><strong>这是什么方程？</strong></p>
                <ul>
                    <li>这是著名的<strong>简谐振动方程</strong></li>
                    <li>解的形式是余弦函数和正弦函数</li>
                    <li>边界条件帮我们选择正确的解</li>
                </ul>
            </div>

            <div class="example">
                <strong>物理类比：</strong><br>
                就像吉他弦的振动，不同的α对应不同的"音调"<br>
                边界条件就像弦的两端固定，限制了可能的振动模式
            </div>

            <h4>应用边界条件</h4>
            <div class="formula-breakdown">
                <p><strong>通解：</strong>X(x) = A cos(αx) + B sin(αx)</p>
                <p><strong>边界条件 X'(0) = 0：</strong></p>
                <ul>
                    <li>X'(x) = -Aα sin(αx) + Bα cos(αx)</li>
                    <li>X'(0) = Bα = 0，所以 B = 0</li>
                    <li>得到：X(x) = A cos(αx)</li>
                </ul>
            </div>

            <div class="formula-breakdown">
                <p><strong>边界条件 X'(L) = 0：</strong></p>
                <ul>
                    <li>X'(L) = -Aα sin(αL) = 0</li>
                    <li>要使A ≠ 0（非平凡解），必须 sin(αL) = 0</li>
                    <li>所以：αL = nπ，即 α = nπ/L</li>
                </ul>
            </div>

            <div class="key-insight">
                <h4>🎯 X方向的解</h4>
                <div class="math-simple" style="background: rgba(255,255,255,0.2); border: none;">
                    X_n(x) = cos(nπx/L), n = 0,1,2,3,...
                </div>
                <p>这些就是X方向的"基本音符"！</p>
            </div>
        </div>

        <h2>🔄 Y方向 - 完全相同的过程</h2>
        <div class="step">
            <div class="step-number">8</div>
            <h3>Y方向的特征函数</h3>

            <div class="intuition">
                Y方向的方程和边界条件与X方向完全相同，所以解也类似
            </div>

            <div class="math-simple">
                Y_m(y) = cos(mπy/L), m = 0,1,2,3,...
            </div>

            <div class="example">
                <strong>对称性的美妙：</strong><br>
                由于我们研究的是正方形区域，X和Y方向具有相同的性质<br>
                这种对称性大大简化了问题！
            </div>
        </div>

        <h2>⏰ 时间方程 - 指数衰减</h2>
        <div class="step">
            <div class="step-number">9</div>
            <h3>时间演化的规律</h3>

            <div class="intuition">
                时间方程告诉我们：不同的"音符"随时间衰减的速度不同
            </div>

            <h4>时间方程</h4>
            <div class="math-simple">
                T'(t)/T(t) = -kλ<br>
                其中：λ = α² + β² = (nπ/L)² + (mπ/L)²
            </div>

            <div class="formula-breakdown">
                <p><strong>这是什么方程？</strong></p>
                <ul>
                    <li>这是<strong>指数衰减方程</strong></li>
                    <li>解的形式：T(t) = e^(-kλt)</li>
                    <li>λ越大，衰减越快</li>
                </ul>
            </div>

            <div class="key-insight">
                <h4>🎯 时间演化的解</h4>
                <div class="math-simple" style="background: rgba(255,255,255,0.2); border: none;">
                    T_{n,m}(t) = e^(-k[(nπ/L)² + (mπ/L)²]t)
                </div>
                <p>高频模式衰减快，低频模式衰减慢！</p>
            </div>

            <div class="example">
                <strong>物理直觉：</strong>
                <ul>
                    <li>🎵 低频（n,m小）：大尺度的温度变化，衰减慢</li>
                    <li>🎶 高频（n,m大）：局部的温度波动，衰减快</li>
                </ul>
                就像音乐中，低音传播远，高音衰减快！
            </div>
        </div>

        <h2>🧩 拼装完整解 - 乐高积木组合</h2>
        <div class="step">
            <div class="step-number">10</div>
            <h3>把所有"音符"组合成完整的"乐曲"</h3>

            <div class="intuition">
                现在我们有了X、Y、T三个方向的基本解，就像有了乐高的基本积木<br>
                接下来要把它们组合成完整的解
            </div>

            <h4>单个解的形式</h4>
            <div class="math-simple">
                u_{n,m}(x,y,t) = X_n(x) × Y_m(y) × T_{n,m}(t)<br><br>
                = cos(nπx/L) × cos(mπy/L) × e^(-k[(nπ/L)² + (mπ/L)²]t)
            </div>

            <div class="example">
                <strong>每个u_{n,m}代表什么？</strong>
                <ul>
                    <li>u_{0,0}：最基本的模式，整体平均温度的衰减</li>
                    <li>u_{1,0}：X方向的第一个振荡模式</li>
                    <li>u_{0,1}：Y方向的第一个振荡模式</li>
                    <li>u_{1,1}：对角方向的振荡模式</li>
                </ul>
            </div>

            <h4>线性叠加原理</h4>
            <div class="formula-breakdown">
                <p><strong>物理原理：</strong>如果u₁和u₂都是解，那么u₁+u₂也是解</p>
                <p><strong>数学原理：</strong>热传导方程是线性的</p>
            </div>

            <div class="key-insight">
                <h4>🎯 完整的通解</h4>
                <div class="math-simple" style="background: rgba(255,255,255,0.2); border: none;">
                    u(x,y,t) = Σ Σ A_{n,m} cos(nπx/L) cos(mπy/L) e^(-λ_{n,m}kt)
                </div>
                <p>其中：λ_{n,m} = (nπ/L)² + (mπ/L)²</p>
                <p>A_{n,m} 是待定系数，由初始条件确定</p>
            </div>
        </div>

        <h2>🎯 确定系数 - 初始条件的作用</h2>
        <div class="step">
            <div class="step-number">11</div>
            <h3>利用初始温度分布确定A_{n,m}</h3>

            <div class="intuition">
                就像调音师根据想要的音乐效果，调整每个音符的音量大小
            </div>

            <h4>初始条件的应用</h4>
            <div class="math-simple">
                在 t = 0 时：<br>
                u(x,y,0) = φ(x,y) = Σ Σ A_{n,m} cos(nπx/L) cos(mπy/L)
            </div>

            <div class="formula-breakdown">
                <p><strong>这是什么？</strong></p>
                <ul>
                    <li>这是<strong>二维傅里叶级数展开</strong></li>
                    <li>任何初始温度分布φ(x,y)都可以表示为余弦函数的组合</li>
                    <li>A_{n,m}就是每个"音符"的"音量"</li>
                </ul>
            </div>

            <h4>系数的计算公式</h4>
            <div class="math-simple">
                A_{n,m} = (4/L²) ∫₀ᴸ ∫₀ᴸ φ(x,y) cos(nπx/L) cos(mπy/L) dx dy
            </div>

            <div class="warning">
                这个积分公式看起来复杂，但核心思想很简单：<br>
                测量初始温度分布φ(x,y)中包含多少"第(n,m)号音符"
            </div>
        </div>

        <h2>💡 神奇的发现 - DCT的出现！</h2>
        <div class="step">
            <div class="step-number">12</div>
            <h3>从连续积分到离散变换</h3>

            <div class="intuition">
                在计算机中，我们不能处理连续的函数，只能处理离散的数据点<br>
                这时候DCT（离散余弦变换）就闪亮登场了！
            </div>

            <h4>连续到离散的转换</h4>
            <div class="comparison-table">
                <tr>
                    <th>连续情况</th>
                    <th>离散情况</th>
                </tr>
                <tr>
                    <td>积分 ∫∫ φ(x,y) cos(...) dx dy</td>
                    <td>求和 Σ Σ φ[i,j] cos(...)</td>
                </tr>
                <tr>
                    <td>连续函数 φ(x,y)</td>
                    <td>数字图像 φ[i,j]</td>
                </tr>
                <tr>
                    <td>无穷多个系数</td>
                    <td>有限个系数（如3×3）</td>
                </tr>
            </table>

            <div class="key-insight">
                <h4>🎯 重大发现</h4>
                <p><strong>系数A_{n,m} ≈ DCT2D(φ)</strong></p>
                <div class="math-simple" style="background: rgba(255,255,255,0.2); border: none;">
                    热传导方程的系数 = 初始温度分布的DCT变换！
                </div>
            </div>

            <div class="example">
                <strong>这意味着什么？</strong>
                <ul>
                    <li>🔥 DCT变换 = 计算热传导方程的系数</li>
                    <li>⏰ 时间演化 = 在频域中应用指数衰减</li>
                    <li>🔄 IDCT变换 = 重构最终的温度分布</li>
                </ul>
            </div>
        </div>

        <h2>🚀 完整的求解流程</h2>
        <div class="step">
            <div class="step-number">13</div>
            <h3>从问题到答案的完整路径</h3>

            <div class="visual-box">
                <h4>🔄 热传导方程求解的完整流程</h4>
                <p><strong>步骤1：</strong>初始温度分布 φ(x,y)</p>
                <p>⬇️</p>
                <p><strong>步骤2：</strong>DCT变换得到系数 A_{n,m} = DCT2D(φ)</p>
                <p>⬇️</p>
                <p><strong>步骤3：</strong>应用时间演化 A_{n,m} × e^(-λ_{n,m}kt)</p>
                <p>⬇️</p>
                <p><strong>步骤4：</strong>IDCT重构 u(x,y,t) = IDCT2D(演化后的系数)</p>
            </div>

            <div class="example">
                <strong>具体例子：3×3温度网格</strong>
                <ol>
                    <li>输入：3×3的初始温度矩阵</li>
                    <li>DCT：得到3×3的频率系数矩阵</li>
                    <li>衰减：每个系数乘以对应的衰减因子</li>
                    <li>IDCT：得到3×3的最终温度矩阵</li>
                </ol>
            </div>

            <div class="key-insight">
                <h4>🎯 vHeat的核心洞察</h4>
                <p>vHeat模型巧妙地利用了这个数学联系：</p>
                <ul>
                    <li>🖼️ 图像patch = 初始温度分布</li>
                    <li>🔄 DCT/IDCT = 高效求解热传导方程</li>
                    <li>📡 全局感受野 = 热量传播到整个区域</li>
                    <li>⚡ 高效计算 = O(N log N)复杂度</li>
                </ul>
            </div>
        </div>

        <h2>🎉 总结：从物理到AI的美妙旅程</h2>
        <div class="step">
            <div class="step-number">✨</div>
            <h3>我们学到了什么？</h3>

            <div class="key-insight">
                <h4>🌟 核心收获</h4>
                <ol>
                    <li><strong>物理直觉：</strong>热传导是自然界的基本现象</li>
                    <li><strong>数学建模：</strong>偏微分方程描述物理规律</li>
                    <li><strong>求解技巧：</strong>分离变量法化繁为简</li>
                    <li><strong>数学美感：</strong>余弦函数是自然的"基本音符"</li>
                    <li><strong>计算实现：</strong>DCT是求解的高效工具</li>
                    <li><strong>AI应用：</strong>物理原理启发深度学习创新</li>
                </ol>
            </div>

            <div class="visual-box">
                <h4>🔥 vHeat的创新价值</h4>
                <p><strong>理论优雅：</strong>基于成熟的物理学原理</p>
                <p><strong>计算高效：</strong>利用DCT的快速算法</p>
                <p><strong>效果卓越：</strong>实现全局感受野</p>
                <p><strong>启发意义：</strong>为AI模型设计开辟新思路</p>
            </div>

            <div class="intuition">
                <strong>最后的思考：</strong><br>
                科学的美妙之处在于，看似不相关的领域（物理学的热传导 vs 计算机视觉）<br>
                通过数学的桥梁，竟然能够完美结合，创造出令人惊叹的技术创新！
            </div>
        </div>

        <script>
            // 简单的进度条动画
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const rate = scrolled / (document.body.scrollHeight - window.innerHeight);
                document.getElementById('progress').style.width = (rate * 100) + '%';
            });
        </script>
    </div>
</body>
</html>
